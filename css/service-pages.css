/* Service Pages Styles */

/* Hero Section */
.hero-section {
  height: 50vh;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-position: center;
  position: relative;
  text-align: center;
  padding: 2rem;
}

.hero-content {
  max-width: 900px;
  margin: 0 auto;
  z-index: 2;
}

.page-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  color: var(--text-light);
}

.page-subtitle {
  font-size: 1.8rem;
  color: var(--text-light);
  margin-bottom: 2rem;
  font-weight: 400;
}

/* Service Content Section */
.service-content-section {
  padding: 5rem 0;
}

.service-content {
  max-width: 900px;
  margin: 0 auto;
}

.service-intro {
  font-size: 1.3rem;
  line-height: 1.7;
  margin-bottom: 3rem;
  color: var(--text-light);
}

.service-benefits,
.service-process,
.service-types,
.service-conclusion {
  margin-bottom: 4rem;
}

.service-benefits h2,
.service-process h2,
.service-types h2,
.service-conclusion h2 {
  font-size: 2.2rem;
  margin-bottom: 1.5rem;
  color: var(--text-light);
  position: relative;
  padding-bottom: 0.8rem;
}

.service-benefits h2::after,
.service-process h2::after,
.service-types h2::after,
.service-conclusion h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 1.5px;
}

.service-benefits p,
.service-process p,
.service-types p,
.service-conclusion p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-gray);
  margin-bottom: 1.5rem;
}

/* Benefits List */
.benefits-list {
  list-style: none;
  padding: 0;
  margin: 2rem 0;
}

.benefits-list li {
  margin-bottom: 1rem;
  padding-left: 2rem;
  position: relative;
  color: var(--text-light);
  font-size: 1.1rem;
}

.benefits-list li i {
  position: absolute;
  left: 0;
  top: 0.3rem;
  color: var(--primary-color);
}

/* Process Steps */
.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.process-step {
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 2rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.process-step:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 48, 48, 0.1);
}

.process-step h3 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.process-step p {
  color: var(--text-gray);
  margin-bottom: 0;
}

/* Service CTA */
.service-cta {
  text-align: center;
  margin: 4rem 0 2rem;
}

.service-cta .btn {
  padding: 1.2rem 3rem;
  font-size: 1.2rem;
}

/* FAQ Section */
.faq-section {
  margin-bottom: 4rem;
}

.faq-section h2 {
  font-size: 2.2rem;
  margin-bottom: 2rem;
  color: var(--text-light);
  position: relative;
  padding-bottom: 0.8rem;
}

.faq-section h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 1.5px;
}

/* Accordion Styling */
.faq-item {
  margin-bottom: 1rem;
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.faq-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.faq-question {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-light);
  padding: 1.2rem;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
}

.faq-question:hover {
  color: var(--primary-color);
}

.faq-question::after {
  content: '\f107';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  color: var(--primary-color);
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.faq-item.active .faq-question::after {
  transform: rotate(180deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s ease;
  padding: 0 1.2rem;
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-gray);
}

.faq-item.active .faq-answer {
  max-height: 1000px;
  padding: 0 1.2rem 1.2rem;
}

.faq-answer ul {
  list-style: none;
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.faq-answer ul li {
  position: relative;
  margin-bottom: 0.8rem;
  padding-left: 1rem;
}

.faq-answer ul li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--primary-color);
}

/* Legal Pages Styling */
.legal-section {
  max-width: 800px;
  margin: 0 auto;
}

.legal-section h2 {
  font-size: 2rem;
  margin: 2.5rem 0 1.5rem;
  color: var(--text-light);
  position: relative;
  padding-bottom: 0.8rem;
}

.legal-section h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 1.5px;
}

.legal-section h2:first-child {
  margin-top: 0;
}

.legal-section h3 {
  font-size: 1.5rem;
  margin: 2rem 0 1rem;
  color: var(--text-light);
}

.legal-section p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-gray);
  margin-bottom: 1.5rem;
}

.legal-section ul {
  list-style: none;
  padding-left: 1.5rem;
  margin-bottom: 1.5rem;
}

.legal-section ul li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.8rem;
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-gray);
}

.legal-section ul li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-size: 1.2rem;
}

.legal-section a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.3s ease;
}

.legal-section a:hover {
  text-decoration: underline;
  opacity: 0.9;
}

.legal-update {
  margin-top: 3rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-style: italic;
  color: var(--text-gray);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .page-title {
    font-size: 3rem;
  }

  .page-subtitle {
    font-size: 1.5rem;
  }

  .service-content-section {
    padding: 4rem 2rem;
  }

  .legal-section h2 {
    font-size: 1.8rem;
  }

  .legal-section h3 {
    font-size: 1.4rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 300px;
  }

  .page-title {
    font-size: 2.5rem;
  }

  .page-subtitle {
    font-size: 1.3rem;
  }

  .service-intro {
    font-size: 1.2rem;
  }

  .service-benefits h2,
  .service-process h2,
  .service-types h2,
  .service-conclusion h2 {
    font-size: 1.8rem;
  }

  .process-steps {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .page-title {
    font-size: 2rem;
  }

  .page-subtitle {
    font-size: 1.1rem;
  }

  .service-content-section {
    padding: 3rem 1.5rem;
  }

  .service-cta .btn {
    width: 100%;
  }
}

/* Showcase Page Specific Styles */
.showcase-video-container {
  margin: 4rem 0;
  padding: 0;
}

.showcase-video-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.showcase-video-wrapper h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 2rem;
  color: var(--text-color);
}

.vimeo-container {
  position: relative;
  width: 100%;
  height: 850px; /* Increased from 675px for much bigger videos */
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  background-color: var(--dark-bg);
  margin-bottom: 2rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.vimeo-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
}

.vimeo-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 15px;
}

.video-description {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.video-description p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
}

/* CTA Section for Showcase */
.cta-section {
  text-align: center;
  margin-top: 3rem;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, var(--primary-color), #ff4444);
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 48, 48, 0.3);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 48, 48, 0.4);
  color: white;
}

/* Responsive Design for Showcase */
@media (max-width: 1024px) {
  .vimeo-container {
    height: 700px; /* Increased for bigger videos */
  }

  .showcase-video-wrapper h2 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .vimeo-container {
    height: 500px; /* Increased for bigger videos */
  }

  .showcase-video-wrapper {
    padding: 0 1rem;
  }

  .showcase-video-wrapper h2 {
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  .vimeo-container {
    height: 400px; /* Increased for bigger videos */
  }

  .showcase-video-wrapper h2 {
    font-size: 1.5rem;
  }

  .video-description p {
    font-size: 1rem;
  }
}
