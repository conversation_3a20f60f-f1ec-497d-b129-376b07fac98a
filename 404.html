<!doctype html>
<html lang="de">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>404 - Seite nicht gefunden | Heart & Soul Film- und Medienproduktion</title>
  <link rel="stylesheet" href="css/style_fixed.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/locomotive-scroll@4.1.4/dist/locomotive-scroll.min.css">
  <link rel="stylesheet" href="css/locomotive-scroll-basic.css">
  <link rel="stylesheet" href="css/animations.css">
  <meta name="description" content="404 - Die gesuchte Seite wurde nicht gefunden. Heart & Soul Film- und Medienproduktion.">

  <link rel="icon" href="Hs-favicon.webp" type="image/webp">
  <link rel="icon" href="/favicon.ico" sizes="any">
  <link rel="icon" href="/icon.svg" type="image/svg+xml">
  <link rel="apple-touch-icon" href="icon.png">

  <link rel="manifest" href="site.webmanifest">
  <meta name="theme-color" content="#121212">

  <style>
    .error-section {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8rem 2rem;
    }

    .error-container {
      max-width: 800px;
      margin: 0 auto;
      text-align: center;
      background: rgba(255, 255, 255, 0.03);
      border-radius: 20px;
      padding: 4rem 3rem;
      backdrop-filter: blur(10px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(255, 255, 255, 0.05);
      animation: fadeIn 1s ease;
    }

    .error-icon {
      font-size: 5rem;
      color: var(--primary-color);
      margin-bottom: 2rem;
      animation: pulse 2s infinite;
    }

    .error-title {
      font-size: 3.5rem;
      font-weight: 700;
      margin-bottom: 1.5rem;
      color: var(--text-light);
    }

    .error-subtitle {
      font-size: 1.5rem;
      color: var(--primary-color);
      margin-bottom: 2rem;
      font-weight: 500;
    }

    .error-text {
      font-size: 1.2rem;
      color: var(--text-gray);
      margin-bottom: 3rem;
      line-height: 1.8;
    }

    .error-buttons {
      display: flex;
      justify-content: center;
      gap: 1.5rem;
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.1);
      }
      100% {
        transform: scale(1);
      }
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @media (max-width: 768px) {
      .error-container {
        padding: 3rem 2rem;
      }

      .error-title {
        font-size: 2.5rem;
      }

      .error-subtitle {
        font-size: 1.3rem;
      }

      .error-text {
        font-size: 1.1rem;
      }

      .error-buttons {
        flex-direction: column;
        gap: 1rem;
      }

      .error-buttons .btn {
        width: 100%;
      }
    }
  </style>
</head>

<body data-scroll-container>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <a href="index.html"><img src="HeartAndSoul-Logo.png" alt="Heart & Soul Medienproduktion"></a>
      </div>
      <div class="nav-links">
        <a href="index.html">Home</a>
        <a href="portfolio.html">Portfolio</a>
        <a href="leistungen.html">Leistungen</a>
        <a href="kontakt.html">Kontakt</a>
      </div>
      <button class="mobile-menu-btn">
        <i class="fas fa-bars"></i>
      </button>
    </div>
  </nav>
  <!-- Mobile Menu (separate from navbar for better structure) -->
  <div class="mobile-menu">
    <a href="index.html">Home</a>
    <a href="portfolio.html">Portfolio</a>
    <a href="leistungen.html">Leistungen</a>
    <a href="kontakt.html">Kontakt</a>
  </div>

  <!-- 404 Error Section -->
  <section class="error-section" data-scroll-section>
    <div class="error-container">
      <div class="error-icon">
        <i class="fas fa-film"></i>
      </div>
      <h1 class="error-title">404</h1>
      <h2 class="error-subtitle">Seite nicht gefunden</h2>
      <p class="error-text">Die von Ihnen gesuchte Seite existiert leider nicht. Möglicherweise wurde sie verschoben, gelöscht oder die URL wurde falsch eingegeben.</p>
      <div class="error-buttons">
        <a href="index.html" class="btn btn-primary">Zurück zur Startseite</a>
        <a href="kontakt.html" class="btn btn-outline">Kontakt aufnehmen</a>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer" data-scroll-section>
    <div class="footer-container">
      <div>
        <div class="footer-logo">
          <img src="HeartAndSoul-Logo.png" alt="Heart & Soul Medienproduktion">
        </div>
        <p class="footer-description">Professionelle Videoproduktion mit Herz und Seele. Wir bringen Ihre Botschaft zum Leben.</p>
        <div class="social-links">
          <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
          <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
          <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
          <a href="https://www.linkedin.com/in/malte-strömsdörfer-998b55112" class="social-link" target="_blank" rel="noopener"><i class="fab fa-linkedin-in"></i></a>
        </div>
      </div>
      <div>
        <h3 class="footer-title">Leistungen</h3>
        <ul class="footer-links">
          <li><a href="werbefilm.html">Werbefilme</a></li>
          <li><a href="imagefilm.html">Imagefilme</a></li>
          <li><a href="recruiting.html">Recruitingfilme</a></li>
          <li><a href="leistungen.html">Alle Leistungen</a></li>
        </ul>
      </div>
      <div>
        <h3 class="footer-title">Kontakt</h3>
        <ul class="footer-links">
          <li><i class="fas fa-phone mr-2"></i> <a href="tel:+4917650200617">+49 (0) 176 50200617</a></li>
          <li><i class="fas fa-envelope mr-2"></i> <a href="mailto:<EMAIL>"><EMAIL></a></li>
        </ul>      </div>
    </div>
    <div class="copyright">
      <div class="footer-legal">
        <a href="impressum.html">Impressum</a>
        <a href="datenschutz.html">Datenschutz</a>
        <a href="agb.html">AGB</a>
      </div>
      <div class="copyright-text">
        &copy; 2024 Heart & Soul Medienproduktion UG (haftungsbeschränkt). Alle Rechte vorbehalten.
        
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/locomotive-scroll@4.1.4/dist/locomotive-scroll.min.js"></script>
  <script src="js/app.js"></script>
  <script src="js/smooth-scroll-basic.js"></script>
</body>

</html>
